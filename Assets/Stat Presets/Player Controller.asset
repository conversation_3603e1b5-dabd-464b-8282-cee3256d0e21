%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 10b823639e543694d890e5f8a77be1af, type: 3}
  m_Name: Player Controller
  m_EditorClassIdentifier: 
  PlayerLayer:
    serializedVersion: 2
    m_Bits: 0
  PlayerTag: Player
  SnapInput: 1
  VerticalDeadZoneThreshold: 0.3
  HorizontalDeadZoneThreshold: 0.1
  MaxSpeed: 14
  Acceleration: 120
  GroundDeceleration: 60
  AirDeceleration: 30
  GroundingForce: -1.5
  GrounderDistance: 0.05
  JumpPower: 36
  MaxFallSpeed: 40
  FallAcceleration: 110
  JumpEndEarlyGravityModifier: 3
  CoyoteTime: 0.15
  JumpBuffer: 0.2
  DashPower: 40
  DashDuration: 0.2
  DashDistance: 0.001
  CrouchHeightReduction: 0.5
  SwimSpeed: 5
  Buoyancy: 5
  SwimDrag: 2
  SwimRotationSpeed: 10
  GlideFallSpeed: 2
  GlideForwardSpeed: 4
  GlideTiltAngle: 15
  GlideRotationSpeed: 10
  ClimbSpeed: 5
  RopeJumpPower: {x: 20, y: 20}
  RopeCooldown: 0.5
  WallGripDuration: 0.07
  WallJumpForce: {x: 15, y: 40}
  WallCheckDistance: 0.6
  WallSlideSpeed: 10
  WallLayer:
    serializedVersion: 2
    m_Bits: 64
  SwimDeceleration: 8
  SwimPropulsionForce: 12
  SwimDecelerationDuration: 0.8
  SwimPropulsionDuration: 0.4
  SwimMaxSpeed: 8
  SwimMinSpeed: 2
  CanJump: 1
  CanDash: 1
  CanWallClimb: 1
  CanSwim: 1
  CanRopeClimb: 1
  CanGlide: 1
