using System;
using UnityEngine;
using AnchorLight.Game.Characters.States;

[RequireComponent(typeof(Rigidbody2D), typeof(Collider2D))]
public class PlayerController : Mono<PERSON><PERSON><PERSON>our, IPlayerController
{
    [SerializeField]
    internal ScriptableStats _stats;

    // Core components - made internal for state access
    internal Rigidbody2D _rb;
    internal CapsuleCollider2D _col;
    internal FrameInput _frameInput;
    internal Vector2 _frameVelocity;
    private bool _cachedQueryStartInColliders;

    // State machine
    private PlayerStateMachine _stateMachine;

    // Movement state variables - made internal for state access
    internal bool _dashToConsume;
    internal bool _isDashing;
    internal float _dashTimeLeft;
    internal Vector2 _dashDirection;
    internal float _dashTraveled;
    internal float _lastFacing = 1f;

    internal bool _isCrouching;
    internal bool _isSwimming;
    internal bool _isDiving;
    internal bool _isSubmerged;
    internal float _origColHeight;
    internal Vector2 _origColOffset;
    internal Collider2D _waterCollider;

    // NEW: Gliding state
    internal bool _isGliding;

    // NEW: Climbing state
    internal bool _isClimbing;
    internal Rope _currentRope;
    internal RopeInteractionZone _currentRopeZone;

    // Wall climbing state
    internal bool _isWallClimbing;
    internal float _wallGripTimeLeft;
    internal int _wallDirection; // -1 for left wall, 1 for right wall

    // IPlayerController implementation
    public bool IsSwimming => _isSwimming;
    public bool IsDiving => _isDiving;
    public bool IsWallClimbing => _isWallClimbing;

    public bool IsClimbingOnRope(Rope rope) => _isClimbing && _currentRope == rope;

    #region Interface

    public Vector2 FrameInput => _frameInput.Move;
    public event Action<bool, float> GroundedChanged;
    public event Action Jumped;

    #endregion

    internal float _time;

    private void Awake()
    {
        _rb = GetComponent<Rigidbody2D>();
        _col = GetComponent<CapsuleCollider2D>();
        // Cache original collider dimensions for crouch
        _origColHeight = _col.size.y;
        _origColOffset = _col.offset;

        _cachedQueryStartInColliders = Physics2D.queriesStartInColliders;

        // Initialize state machine
        InitializeStateMachine();
    }

    private void InitializeStateMachine()
    {
        _stateMachine = new PlayerStateMachine(this);

        // Register all states
        _stateMachine.RegisterState(new GroundedState());
        _stateMachine.RegisterState(new AirborneState());
        _stateMachine.RegisterState(new SwimmingState());
        _stateMachine.RegisterState(new DashingState());
        _stateMachine.RegisterState(new GlidingState());
        _stateMachine.RegisterState(new WallClimbingState());
        _stateMachine.RegisterState(new RopeClimbingState());

        // Start with grounded state
        _stateMachine.Initialize(new GroundedState());
    }

    private void Update()
    {
        _time += Time.deltaTime;
        GatherInput();
        HandleRopeLatching();
    }

    private void GatherInput()
    {
        _frameInput = new FrameInput
        {
            JumpDown = Input.GetButtonDown("Jump"),
            JumpHeld = Input.GetButton("Jump"),
            Move = new Vector2(Input.GetAxisRaw("Horizontal"), Input.GetAxisRaw("Vertical")),
            DashDown = Input.GetKeyDown(KeyCode.C),
            CrouchHeld = Input.GetKey(KeyCode.X),
            // NEW: Added Glide input
            GlideHeld = Input.GetKey(KeyCode.G),
            InteractDown = Input.GetKeyDown(KeyCode.E),
        };

        if (_stats.SnapInput)
        {
            _frameInput.Move.x =
                Mathf.Abs(_frameInput.Move.x) < _stats.HorizontalDeadZoneThreshold
                    ? 0
                    : Mathf.Sign(_frameInput.Move.x);
            _frameInput.Move.y =
                Mathf.Abs(_frameInput.Move.y) < _stats.VerticalDeadZoneThreshold
                    ? 0
                    : Mathf.Sign(_frameInput.Move.y);
        }

        if (_frameInput.Move.x != 0)
            _lastFacing = _frameInput.Move.x;

        if (_frameInput.JumpDown)
        {
            _jumpToConsume = true;
            _timeJumpWasPressed = _time;
        }

        if (_frameInput.DashDown)
            _dashToConsume = true;
    }

    private void FixedUpdate()
    {
        CheckCollisions();
        HandleRopeLatching();

        // Update state machine - this handles all movement logic
        _stateMachine.Update();
    }

    #region Collisions

    internal float _frameLeftGrounded = float.MinValue;
    internal bool _grounded;

    private void CheckCollisions()
    {
        Physics2D.queriesStartInColliders = false;

        // Ground and Ceiling
        bool groundHit = Physics2D.CapsuleCast(
            _col.bounds.center,
            _col.size,
            _col.direction,
            0,
            Vector2.down,
            _stats.GrounderDistance,
            ~_stats.PlayerLayer
        );
        bool ceilingHit = Physics2D.CapsuleCast(
            _col.bounds.center,
            _col.size,
            _col.direction,
            0,
            Vector2.up,
            _stats.GrounderDistance,
            ~_stats.PlayerLayer
        );

        // Wall detection
        CheckWallCollisions();

        // Hit a Ceiling
        if (ceilingHit)
            _frameVelocity.y = Mathf.Min(0, _frameVelocity.y);

        // Landed on the Ground
        if (!_grounded && groundHit)
        {
            _grounded = true;
            _coyoteUsable = true;
            _bufferedJumpUsable = true;
            _endedJumpEarly = false;
            _isGliding = false; // NEW: Stop gliding on ground
            _isWallClimbing = false; // Stop wall climbing on ground
            GroundedChanged?.Invoke(true, Mathf.Abs(_frameVelocity.y));
        }
        // Left the Ground
        else if (_grounded && !groundHit)
        {
            _grounded = false;
            _frameLeftGrounded = _time;
            GroundedChanged?.Invoke(false, 0);
        }

        Physics2D.queriesStartInColliders = _cachedQueryStartInColliders;
    }

    private void CheckWallCollisions()
    {
        // Don't attach to walls if grounded, swimming, rope climbing, or dashing
        if (_grounded || _isSwimming || _isClimbing || _isDashing)
        {
            _isWallClimbing = false;
            return;
        }

        // Check for walls on both sides
        bool leftWall = Physics2D.Raycast(
            _col.bounds.center,
            Vector2.left,
            _stats.WallCheckDistance,
            _stats.WallLayer
        );
        bool rightWall = Physics2D.Raycast(
            _col.bounds.center,
            Vector2.right,
            _stats.WallCheckDistance,
            _stats.WallLayer
        );

        // Determine if we should attach to a wall
        bool shouldAttachToWall = false;
        int newWallDirection = 0;

        // Attach to left wall if moving left and hitting left wall
        if (leftWall && _frameInput.Move.x < 0)
        {
            shouldAttachToWall = true;
            newWallDirection = -1;
        }
        // Attach to right wall if moving right and hitting right wall
        else if (rightWall && _frameInput.Move.x > 0)
        {
            shouldAttachToWall = true;
            newWallDirection = 1;
        }

        // Start wall climbing if conditions are met
        if (shouldAttachToWall && !_isWallClimbing)
        {
            _isWallClimbing = true;
            _wallDirection = newWallDirection;
            _wallGripTimeLeft = _stats.WallGripDuration;
            _frameVelocity.x = 0; // Stop horizontal movement when attaching
        }
        // Stop wall climbing if no longer touching wall or changed direction
        else if (_isWallClimbing && (!leftWall && !rightWall ||
                 (_wallDirection == -1 && !leftWall) ||
                 (_wallDirection == 1 && !rightWall)))
        {
            _isWallClimbing = false;
        }
    }

    #endregion


    #region Jumping

    internal bool _jumpToConsume;
    internal bool _bufferedJumpUsable;
    internal bool _endedJumpEarly;
    internal bool _coyoteUsable;
    internal float _timeJumpWasPressed;

    // Helper properties for states to use
    internal bool HasBufferedJump =>
        _bufferedJumpUsable && _time < _timeJumpWasPressed + _stats.JumpBuffer;
    internal bool CanUseCoyote =>
        _coyoteUsable && !_grounded && _time < _frameLeftGrounded + _stats.CoyoteTime;



    #endregion



    /// <summary>
    /// Adjusts the capsule collider for crouch height reduction when holding X and restores on release
    /// </summary>
    internal void HandleCrouch()
    {
        var stats = _stats;
        bool wantCrouch = _grounded && _frameInput.CrouchHeld;
        // Shrink collider when starting crouch
        if (wantCrouch && !_isCrouching)
        {
            _isCrouching = true;
            var newHeight = Mathf.Max(0.1f, _origColHeight - stats.CrouchHeightReduction);
            _col.size = new Vector2(_col.size.x, newHeight);
            _col.offset = _origColOffset - new Vector2(0, stats.CrouchHeightReduction / 2f);
        }
        // Restore collider when releasing crouch
        else if (!wantCrouch && _isCrouching)
        {
            _isCrouching = false;
            _col.size = new Vector2(_col.size.x, _origColHeight);
            _col.offset = _origColOffset;
        }
    }

    internal void HandleSubmerging()
    {
        var stats = _stats;
        bool wantDive = _isSwimming && _frameInput.CrouchHeld;
        if (wantDive && !_isDiving)
        {
            _isDiving = true;
            var newHeight = Mathf.Max(0.1f, _origColHeight - stats.CrouchHeightReduction);
            _col.size = new Vector2(_col.size.x, newHeight);
            _col.offset = _origColOffset - new Vector2(0, stats.CrouchHeightReduction / 2f);
        }
        else if (!wantDive && _isDiving)
        {
            _isDiving = false;
            _col.size = new Vector2(_col.size.x, _origColHeight);
            _col.offset = _origColOffset;
        }
    }

    /// <summary>
    /// Swim movement: use input for 2D movement, disable dash/jump
    /// </summary>
    internal void HandleSwimming()
    {
        if (_waterCollider == null)
            return;

        var move = _frameInput.Move;
        var waterSurfaceY = _waterCollider.bounds.max.y;
        var playerTopY = _col.bounds.max.y;

        // Prevent upward movement if at or above the surface
        if (playerTopY >= waterSurfaceY && move.y > 0)
        {
            move.y = 0;
        }

        // Calculate horizontal and vertical velocity based on input
        if (move.magnitude > 0.1f)
        {
            _frameVelocity = move.normalized * _stats.SwimSpeed;
        }
        else
        {
            // Apply drag when there's no input
            _frameVelocity = Vector2.MoveTowards(
                _frameVelocity,
                Vector2.zero,
                _stats.SwimDrag * Time.fixedDeltaTime
            );
        }

        // Apply buoyancy only when submerged and not actively moving vertically.
        if (playerTopY < waterSurfaceY && Mathf.Abs(move.y) < 0.1f)
        {
            _frameVelocity.y = Mathf.MoveTowards(
                _frameVelocity.y,
                _stats.Buoyancy,
                _stats.FallAcceleration * Time.fixedDeltaTime
            );
        }
    }

    private void OnTriggerEnter2D(Collider2D other)
    {
        if (other.CompareTag("Water"))
        {
            _isSwimming = true;
            _isSubmerged = true;
            _waterCollider = other;
        }
        else if (other.CompareTag("BouncePad"))
        {
            // Only trigger when falling onto it
            if (_rb.linearVelocity.y <= -0.1f)
            {
                ExecuteBouncePad(other.GetComponent<BouncePad>());
            }
        }
    }

    public void OnEnterRopeZone(RopeInteractionZone zone)
    {
        _currentRopeZone = zone;
    }

    public void OnExitRopeZone(RopeInteractionZone zone)
    {
        if (_currentRopeZone == zone)
        {
            _currentRopeZone = null;
        }
    }

    private void ExecuteBouncePad(BouncePad pad)
    {
        if (pad == null || !pad.CanBounce)
            return;

        _frameVelocity.y = pad.BounceForce; // Apply the bounce to vertical velocity

        pad.TriggerEffects(); // Play Animation/Sound and start cooldown on pad side
    }

    private void OnTriggerExit2D(Collider2D other)
    {
        if (other.CompareTag("Water"))
        {
            _isSwimming = false;
            _isSubmerged = false;
            _waterCollider = null;
        }
    }

    internal void HandleRopeLatching()
    {
        // If E is pressed and we are in the top zone...
        if (_frameInput.InteractDown && _currentRopeZone != null)
        {
            // ...latch to the top point, regardless of whether we are already climbing.
            // This allows overriding the automatic climb.
            transform.position = _currentRopeZone.GetLatchPoint();
            SetClimbing(true, _currentRopeZone.Rope);
        }
    }

    public void SetClimbing(bool climbing, Rope rope = null)
    {
        _isClimbing = climbing;
        _currentRope = rope;

        if (climbing)
        {
            _rb.linearVelocity = Vector2.zero; // Stop movement when latching
            _rb.rotation = 0;
            if (rope != null)
            {
                if (rope.Direction == RopeDirection.Left)
                {
                    _lastFacing = -1;
                }
                else if (rope.Direction == RopeDirection.Right)
                {
                    _lastFacing = 1;
                }
            }
        }
    }

    public void ExitRope(Rope rope)
    {
        if (!_isClimbing || _currentRope != rope)
            return;

        _isClimbing = false;
        _currentRope.StartCooldown(_stats.RopeCooldown);
        transform.position = _currentRope.WorldPlayerExitPoint;
        _frameVelocity = new Vector2(
            _currentRope.ExitForce.x * _lastFacing,
            _currentRope.ExitForce.y
        );
    }

    internal void HandleClimbing()
    {
        if (_currentRope == null)
        {
            _isClimbing = false;
            return;
        }
        if (_jumpToConsume)
        {
            ExecuteRopeJump();
            return;
        }

        var move = _frameInput.Move;
        _frameVelocity.x = 0;
        _frameVelocity.y = move.y * _stats.ClimbSpeed;
    }

    internal void HandleWallClimbing()
    {
        // Handle wall jump
        if (_jumpToConsume)
        {
            ExecuteWallJump();
            return;
        }

        // Update grip timer
        _wallGripTimeLeft -= Time.fixedDeltaTime;

        // If grip time expired, start sliding down
        if (_wallGripTimeLeft <= 0)
        {
            _frameVelocity.x = 0;
            _frameVelocity.y = Mathf.MoveTowards(
                _frameVelocity.y,
                -_stats.WallSlideSpeed,
                _stats.FallAcceleration * Time.fixedDeltaTime
            );
        }
        else
        {
            // Still gripping - no movement
            _frameVelocity.x = 0;
            _frameVelocity.y = 0;
        }
    }

    public void ApplyVelocity(Vector2 vel)
    {
        _frameVelocity = vel;
    }

    private void ExecuteWallJump()
    {
        _isWallClimbing = false;

        // Jump away from the wall at an angle based on current movement input
        Vector2 jumpDirection;

        if (_frameInput.Move.x != 0)
        {
            // Jump in the direction the player is pointing
            jumpDirection = new Vector2(
                _frameInput.Move.x * _stats.WallJumpForce.x,
                _stats.WallJumpForce.y
            );
        }
        else
        {
            // Default jump away from wall
            jumpDirection = new Vector2(
                -_wallDirection * _stats.WallJumpForce.x,
                _stats.WallJumpForce.y
            );
        }

        _frameVelocity = jumpDirection;
        _jumpToConsume = false;
        _endedJumpEarly = false;
        _timeJumpWasPressed = 0;
        _bufferedJumpUsable = false;
        _coyoteUsable = false;
        Jumped?.Invoke();
    }

    private void ExecuteRopeJump()
    {
        _isClimbing = false;
        _frameVelocity = new Vector2(_stats.RopeJumpPower.x * _lastFacing, _stats.RopeJumpPower.y);
        _jumpToConsume = false;
        _endedJumpEarly = false;
        _timeJumpWasPressed = 0;
        _bufferedJumpUsable = false;
        _coyoteUsable = false;
        Jumped?.Invoke();
    }

#if UNITY_EDITOR
    private void OnValidate()
    {
        if (_stats == null)
            Debug.LogWarning(
                "Please assign a ScriptableStats asset to the Player Controller's Stats slot",
                this
            );
    }

    private void OnDrawGizmos()
    {
        if (_col == null || _stats == null) return;

        // Draw wall detection rays
        Gizmos.color = _isWallClimbing ? Color.green : Color.red;

        // Left wall detection
        Gizmos.DrawRay(_col.bounds.center, Vector2.left * _stats.WallCheckDistance);

        // Right wall detection
        Gizmos.DrawRay(_col.bounds.center, Vector2.right * _stats.WallCheckDistance);

        // Draw wall climbing state indicator
        if (_isWallClimbing)
        {
            Gizmos.color = Color.yellow;
            Gizmos.DrawWireSphere(_col.bounds.center, 0.5f);
        }
    }
#endif
}

public struct FrameInput
{
    public bool JumpDown;
    public bool JumpHeld;
    public bool DashDown;
    public bool CrouchHeld;
    public bool InteractDown;

    // NEW: Added GlideHeld
    public bool GlideHeld;
    public Vector2 Move;
}

public interface IPlayerController
{
    public event Action<bool, float> GroundedChanged;

    public event Action Jumped;
    public Vector2 FrameInput { get; }
    public bool IsSwimming { get; }
    public bool IsDiving { get; }
    public bool IsWallClimbing { get; }
}
