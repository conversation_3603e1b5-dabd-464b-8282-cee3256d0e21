using UnityEngine;

namespace AnchorLight.Game.Characters.States
{
    /// <summary>
    /// Swimming state with natural animation pattern alternating between deceleration and propulsion phases
    /// </summary>
    public class SwimmingState : BasePlayerState
    {
        public override string StateName => "Swimming";
        public override int Priority => 100; // High priority when in water

        public enum SwimPhase
        {
            Deceleration,
            Propulsion
        }

        private SwimPhase _currentPhase;
        private float _phaseTimer;
        private Vector2 _swimDirection;
        private Collider2D _waterCollider;
        private bool _isDiving;

        protected override void OnEnter()
        {
            _currentPhase = SwimPhase.Deceleration;
            _phaseTimer = 0f;
            _swimDirection = Vector2.zero;
            _waterCollider = _controller._waterCollider;
            _isDiving = false;
        }

        protected override void OnFixedUpdate()
        {
            HandleSubmerging();
            HandleSwimmingMovement();
            HandleRotation();
            ApplyMovement();
        }

        private void HandleSubmerging()
        {
            bool wantDive = Input.CrouchHeld;
            if (wantDive && !_isDiving)
            {
                _isDiving = true;
                var newHeight = Mathf.Max(0.1f, _controller._origColHeight - Stats.CrouchHeightReduction);
                Collider.size = new Vector2(Collider.size.x, newHeight);
                Collider.offset = _controller._origColOffset - new Vector2(0, Stats.CrouchHeightReduction / 2f);
            }
            else if (!wantDive && _isDiving)
            {
                _isDiving = false;
                Collider.size = new Vector2(Collider.size.x, _controller._origColHeight);
                Collider.offset = _controller._origColOffset;
            }
        }

        private void HandleSwimmingMovement()
        {
            if (_waterCollider == null) return;

            var move = Input.Move;
            var waterSurfaceY = _waterCollider.bounds.max.y;
            var playerTopY = Collider.bounds.max.y;

            // Prevent upward movement if at or above the surface
            if (playerTopY >= waterSurfaceY && move.y > 0)
            {
                move.y = 0;
            }

            // Update swim direction based on input
            if (move.magnitude > 0.1f)
            {
                _swimDirection = move.normalized;
            }

            // Update phase timer
            _phaseTimer += Time.fixedDeltaTime;

            // Handle phase transitions
            switch (_currentPhase)
            {
                case SwimPhase.Deceleration:
                    HandleDecelerationPhase();
                    if (_phaseTimer >= Stats.SwimDecelerationDuration)
                    {
                        _currentPhase = SwimPhase.Propulsion;
                        _phaseTimer = 0f;
                    }
                    break;

                case SwimPhase.Propulsion:
                    HandlePropulsionPhase();
                    if (_phaseTimer >= Stats.SwimPropulsionDuration)
                    {
                        _currentPhase = SwimPhase.Deceleration;
                        _phaseTimer = 0f;
                    }
                    break;
            }

            // Apply buoyancy only when submerged and not actively moving vertically
            if (playerTopY < waterSurfaceY && Mathf.Abs(move.y) < 0.1f)
            {
                FrameVelocity = new Vector2(FrameVelocity.x, 
                    Mathf.MoveTowards(FrameVelocity.y, Stats.Buoyancy, Stats.FallAcceleration * Time.fixedDeltaTime));
            }
        }

        private void HandleDecelerationPhase()
        {
            // Gradually slow down due to water resistance
            if (_swimDirection.magnitude > 0.1f)
            {
                // Decelerate towards minimum speed in the swim direction
                var targetVelocity = _swimDirection * Stats.SwimMinSpeed;
                FrameVelocity = Vector2.MoveTowards(FrameVelocity, targetVelocity, Stats.SwimDeceleration * Time.fixedDeltaTime);
            }
            else
            {
                // No input - decelerate to zero
                FrameVelocity = Vector2.MoveTowards(FrameVelocity, Vector2.zero, Stats.SwimDrag * Time.fixedDeltaTime);
            }
        }

        private void HandlePropulsionPhase()
        {
            // Accelerate forward with swimming stroke
            if (_swimDirection.magnitude > 0.1f)
            {
                var targetVelocity = _swimDirection * Stats.SwimMaxSpeed;
                FrameVelocity = Vector2.MoveTowards(FrameVelocity, targetVelocity, Stats.SwimPropulsionForce * Time.fixedDeltaTime);
            }
            else
            {
                // No input - still apply some drag
                FrameVelocity = Vector2.MoveTowards(FrameVelocity, Vector2.zero, Stats.SwimDrag * 0.5f * Time.fixedDeltaTime);
            }
        }

        private void HandleRotation()
        {
            float targetAngle = 0f;

            // Rotate player towards movement direction
            if (Input.Move.magnitude > 0.1f)
            {
                // Subtract 90 degrees to align the player's 'up' with the movement direction
                targetAngle = Mathf.Atan2(Input.Move.y, Input.Move.x) * Mathf.Rad2Deg - 90f;
            }

            HandleRotation(targetAngle, Stats.SwimRotationSpeed);
        }

        public bool IsDiving => _isDiving;
        public SwimPhase CurrentPhase => _currentPhase;
        public float PhaseProgress => _phaseTimer / GetCurrentPhaseDuration();

        private float GetCurrentPhaseDuration()
        {
            return _currentPhase == SwimPhase.Deceleration ? Stats.SwimDecelerationDuration : Stats.SwimPropulsionDuration;
        }
    }
}
