using System.Collections;
using UnityEngine;

public enum RopeDirection
{
    Left,
    Right,
    Both,
}

[RequireComponent(typeof(Collider2D))]
public class Rope : MonoBehaviour
{
    public RopeDirection Direction = RopeDirection.Both;
    public Vector2 ExitForce = new(5, 5);

    [Tooltip("The y-offset from the top of the rope's collider to set the exit point.")]
    public float ExitPointYOffset = 0.5f;
    public Vector2 PlayerExitPosition = new(0, 1);

    [Header("Latching")]
    public Vector2 TopLatchPointOffset = new(0, -1);

    private Collider2D _collider;
    private bool _isOnCooldown;

    public Vector2 WorldPlayerExitPoint => (Vector2)transform.position + PlayerExitPosition;

    public Vector2 TopLatchPoint => (Vector2)transform.position + TopLatchPointOffset;

    private void Awake()
    {
        _collider = GetComponent<Collider2D>();
    }

    public void StartCooldown(float duration)
    {
        StartCoroutine(Cooldown(duration));
    }

    private void OnTriggerEnter2D(Collider2D collision)
    {
        if (_isOnCooldown)
            return;

        var player = collision.GetComponent<PlayerController>();
        if (player == null || player._stats == null)
            return;

        if (collision.CompareTag(player._stats.PlayerTag))
        {
            player.SetClimbing(true, this);
        }
    }

    private void OnTriggerExit2D(Collider2D collision)
    {
        var player = collision.GetComponent<PlayerController>();
        if (player == null)
            return;

        if (collision.CompareTag(player._stats.PlayerTag))
        {
            player.SetClimbing(false, null);
        }
    }

    private IEnumerator Cooldown(float duration)
    {
        _isOnCooldown = true;
        yield return new WaitForSeconds(duration);
        _isOnCooldown = false;
    }

    private void OnDrawGizmos()
    {
        Gizmos.color = Color.yellow;
        var bounds = GetComponent<Collider2D>().bounds;
        var center = bounds.center;
        var size = bounds.size;

        switch (Direction)
        {
            case RopeDirection.Left:
                Gizmos.DrawLine(center, center + Vector3.left * size.x);
                break;
            case RopeDirection.Right:
                Gizmos.DrawLine(center, center + Vector3.right * size.x);
                break;
            case RopeDirection.Both:
                Gizmos.DrawLine(
                    center + Vector3.down * (size.y / 2),
                    center + Vector3.up * (size.y / 2)
                );
                break;
        }

        if (_collider == null)
            _collider = GetComponent<Collider2D>();

        Gizmos.color = Color.green;
        Gizmos.DrawWireSphere(WorldPlayerExitPoint, 0.2f);

        Gizmos.color = Color.blue;
        Gizmos.DrawWireSphere(TopLatchPoint, 0.2f);
    }
}
