using UnityEngine;
using AnchorLight.Game.Characters.States;

namespace AnchorLight.Demo
{
    /// <summary>
    /// Demo script to showcase the new swimming animation system
    /// </summary>
    public class SwimmingDemo : MonoBehaviour
    {
        [Header("Demo Configuration")]
        [SerializeField] private PlayerController playerController;
        [SerializeField] private bool showSwimmingInfo = true;
        [SerializeField] private bool showAbilityStatus = true;

        private SwimmingState _swimmingState;
        private GUIStyle _guiStyle;

        private void Start()
        {
            if (playerController == null)
            {
                playerController = FindObjectOfType<PlayerController>();
            }

            // Initialize GUI style
            _guiStyle = new GUIStyle();
            _guiStyle.fontSize = 16;
            _guiStyle.normal.textColor = Color.white;
        }

        private void OnGUI()
        {
            if (playerController == null) return;

            float yOffset = 10f;
            float lineHeight = 25f;

            // Show current abilities status
            if (showAbilityStatus)
            {
                GUI.Label(new Rect(10, yOffset, 300, lineHeight), "=== PLAYER ABILITIES ===", _guiStyle);
                yOffset += lineHeight;

                var stats = playerController._stats;
                GUI.Label(new Rect(10, yOffset, 300, lineHeight), $"Jump: {(stats.CanJump ? "ENABLED" : "DISABLED")}", _guiStyle);
                yOffset += lineHeight;
                GUI.Label(new Rect(10, yOffset, 300, lineHeight), $"Dash: {(stats.CanDash ? "ENABLED" : "DISABLED")}", _guiStyle);
                yOffset += lineHeight;
                GUI.Label(new Rect(10, yOffset, 300, lineHeight), $"Wall Climb: {(stats.CanWallClimb ? "ENABLED" : "DISABLED")}", _guiStyle);
                yOffset += lineHeight;
                GUI.Label(new Rect(10, yOffset, 300, lineHeight), $"Swimming: {(stats.CanSwim ? "ENABLED" : "DISABLED")}", _guiStyle);
                yOffset += lineHeight;
                GUI.Label(new Rect(10, yOffset, 300, lineHeight), $"Rope Climb: {(stats.CanRopeClimb ? "ENABLED" : "DISABLED")}", _guiStyle);
                yOffset += lineHeight;
                GUI.Label(new Rect(10, yOffset, 300, lineHeight), $"Glide: {(stats.CanGlide ? "ENABLED" : "DISABLED")}", _guiStyle);
                yOffset += lineHeight * 1.5f;
            }

            // Show swimming information
            if (showSwimmingInfo && playerController._isSwimming)
            {
                GUI.Label(new Rect(10, yOffset, 300, lineHeight), "=== SWIMMING STATUS ===", _guiStyle);
                yOffset += lineHeight;

                GUI.Label(new Rect(10, yOffset, 300, lineHeight), $"Swimming: {playerController._isSwimming}", _guiStyle);
                yOffset += lineHeight;
                GUI.Label(new Rect(10, yOffset, 300, lineHeight), $"Diving: {playerController._isDiving}", _guiStyle);
                yOffset += lineHeight;

                // Show swimming parameters
                var stats = playerController._stats;
                GUI.Label(new Rect(10, yOffset, 300, lineHeight), $"Deceleration Rate: {stats.SwimDeceleration}", _guiStyle);
                yOffset += lineHeight;
                GUI.Label(new Rect(10, yOffset, 300, lineHeight), $"Propulsion Force: {stats.SwimPropulsionForce}", _guiStyle);
                yOffset += lineHeight;
                GUI.Label(new Rect(10, yOffset, 300, lineHeight), $"Max Speed: {stats.SwimMaxSpeed}", _guiStyle);
                yOffset += lineHeight;
                GUI.Label(new Rect(10, yOffset, 300, lineHeight), $"Min Speed: {stats.SwimMinSpeed}", _guiStyle);
                yOffset += lineHeight;

                // Show current velocity
                var velocity = playerController._frameVelocity;
                GUI.Label(new Rect(10, yOffset, 300, lineHeight), $"Velocity: ({velocity.x:F2}, {velocity.y:F2})", _guiStyle);
                yOffset += lineHeight;
                GUI.Label(new Rect(10, yOffset, 300, lineHeight), $"Speed: {velocity.magnitude:F2}", _guiStyle);
                yOffset += lineHeight;
            }

            // Show current state information
            yOffset += lineHeight * 0.5f;
            GUI.Label(new Rect(10, yOffset, 300, lineHeight), "=== CURRENT STATES ===", _guiStyle);
            yOffset += lineHeight;

            string activeStates = "";
            if (playerController._grounded) activeStates += "Grounded ";
            if (playerController._isSwimming) activeStates += "Swimming ";
            if (playerController._isDashing) activeStates += "Dashing ";
            if (playerController._isGliding) activeStates += "Gliding ";
            if (playerController._isWallClimbing) activeStates += "WallClimbing ";
            if (playerController._isClimbing) activeStates += "RopeClimbing ";

            if (string.IsNullOrEmpty(activeStates))
                activeStates = "Airborne";

            GUI.Label(new Rect(10, yOffset, 300, lineHeight), activeStates, _guiStyle);
            yOffset += lineHeight * 2f;

            // Show controls
            GUI.Label(new Rect(10, yOffset, 400, lineHeight), "=== CONTROLS ===", _guiStyle);
            yOffset += lineHeight;
            GUI.Label(new Rect(10, yOffset, 400, lineHeight), "WASD/Arrow Keys: Move", _guiStyle);
            yOffset += lineHeight;
            GUI.Label(new Rect(10, yOffset, 400, lineHeight), "Space: Jump", _guiStyle);
            yOffset += lineHeight;
            GUI.Label(new Rect(10, yOffset, 400, lineHeight), "C: Dash", _guiStyle);
            yOffset += lineHeight;
            GUI.Label(new Rect(10, yOffset, 400, lineHeight), "X: Crouch/Dive (in water)", _guiStyle);
            yOffset += lineHeight;
            GUI.Label(new Rect(10, yOffset, 400, lineHeight), "G: Glide (while falling)", _guiStyle);
            yOffset += lineHeight;
            GUI.Label(new Rect(10, yOffset, 400, lineHeight), "E: Interact with ropes", _guiStyle);
        }

        [ContextMenu("Test Swimming Animation")]
        public void TestSwimmingAnimation()
        {
            if (playerController == null) return;

            Debug.Log("=== Swimming Animation Test ===");
            var stats = playerController._stats;
            
            Debug.Log($"Deceleration Duration: {stats.SwimDecelerationDuration}s");
            Debug.Log($"Propulsion Duration: {stats.SwimPropulsionDuration}s");
            Debug.Log($"Total Cycle Time: {stats.SwimDecelerationDuration + stats.SwimPropulsionDuration}s");
            Debug.Log($"Speed Range: {stats.SwimMinSpeed} - {stats.SwimMaxSpeed}");
            Debug.Log($"Deceleration Rate: {stats.SwimDeceleration}");
            Debug.Log($"Propulsion Force: {stats.SwimPropulsionForce}");
        }

        [ContextMenu("Disable All Abilities")]
        public void DisableAllAbilities()
        {
            if (playerController == null || playerController._stats == null) return;

            var stats = playerController._stats;
            stats.CanJump = false;
            stats.CanDash = false;
            stats.CanWallClimb = false;
            stats.CanSwim = false;
            stats.CanRopeClimb = false;
            stats.CanGlide = false;

            Debug.Log("All abilities disabled!");
        }

        [ContextMenu("Enable All Abilities")]
        public void EnableAllAbilities()
        {
            if (playerController == null || playerController._stats == null) return;

            var stats = playerController._stats;
            stats.CanJump = true;
            stats.CanDash = true;
            stats.CanWallClimb = true;
            stats.CanSwim = true;
            stats.CanRopeClimb = true;
            stats.CanGlide = true;

            Debug.Log("All abilities enabled!");
        }
    }
}
