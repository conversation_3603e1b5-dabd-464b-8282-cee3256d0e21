using UnityEngine;
using AnchorLight.Game.Characters.States;

namespace AnchorLight.Tests
{
    /// <summary>
    /// Test script to validate the new PlayerController state machine and ability system
    /// </summary>
    public class PlayerControllerTests : MonoBehaviour
    {
        [Header("Test Configuration")]
        [SerializeField] private PlayerController playerController;
        [SerializeField] private bool runTestsOnStart = false;
        [SerializeField] private bool enableDebugLogging = true;

        private void Start()
        {
            if (runTestsOnStart)
            {
                RunAllTests();
            }
        }

        [ContextMenu("Run All Tests")]
        public void RunAllTests()
        {
            if (playerController == null)
            {
                playerController = FindObjectOfType<PlayerController>();
                if (playerController == null)
                {
                    LogError("No PlayerController found in scene!");
                    return;
                }
            }

            Log("Starting PlayerController Tests...");
            
            TestAbilityToggles();
            TestStateTransitions();
            TestSwimmingSystem();
            
            Log("All tests completed!");
        }

        private void TestAbilityToggles()
        {
            Log("Testing ability toggles...");
            
            var stats = playerController._stats;
            
            // Test that all abilities are enabled by default
            Assert(stats.CanJump, "Jump ability should be enabled by default");
            Assert(stats.CanDash, "Dash ability should be enabled by default");
            Assert(stats.CanWallClimb, "Wall climb ability should be enabled by default");
            Assert(stats.CanSwim, "Swim ability should be enabled by default");
            Assert(stats.CanRopeClimb, "Rope climb ability should be enabled by default");
            Assert(stats.CanGlide, "Glide ability should be enabled by default");
            
            Log("✓ Ability toggles test passed");
        }

        private void TestStateTransitions()
        {
            Log("Testing state transitions...");
            
            // Test that state machine is initialized
            Assert(playerController != null, "PlayerController should exist");
            
            // Note: More detailed state transition tests would require setting up specific scenarios
            // For now, we just verify the system is properly initialized
            
            Log("✓ State transitions test passed");
        }

        private void TestSwimmingSystem()
        {
            Log("Testing swimming system...");
            
            var stats = playerController._stats;
            
            // Test swimming parameters are properly configured
            Assert(stats.SwimDeceleration > 0, "Swim deceleration should be positive");
            Assert(stats.SwimPropulsionForce > 0, "Swim propulsion force should be positive");
            Assert(stats.SwimDecelerationDuration > 0, "Swim deceleration duration should be positive");
            Assert(stats.SwimPropulsionDuration > 0, "Swim propulsion duration should be positive");
            Assert(stats.SwimMaxSpeed > stats.SwimMinSpeed, "Max swim speed should be greater than min speed");
            
            Log("✓ Swimming system test passed");
        }

        private void Assert(bool condition, string message)
        {
            if (!condition)
            {
                LogError($"ASSERTION FAILED: {message}");
            }
            else if (enableDebugLogging)
            {
                Log($"✓ {message}");
            }
        }

        private void Log(string message)
        {
            if (enableDebugLogging)
            {
                Debug.Log($"[PlayerControllerTests] {message}");
            }
        }

        private void LogError(string message)
        {
            Debug.LogError($"[PlayerControllerTests] {message}");
        }

        [ContextMenu("Toggle Jump Ability")]
        public void ToggleJumpAbility()
        {
            if (playerController != null && playerController._stats != null)
            {
                playerController._stats.CanJump = !playerController._stats.CanJump;
                Log($"Jump ability: {(playerController._stats.CanJump ? "ENABLED" : "DISABLED")}");
            }
        }

        [ContextMenu("Toggle Dash Ability")]
        public void ToggleDashAbility()
        {
            if (playerController != null && playerController._stats != null)
            {
                playerController._stats.CanDash = !playerController._stats.CanDash;
                Log($"Dash ability: {(playerController._stats.CanDash ? "ENABLED" : "DISABLED")}");
            }
        }

        [ContextMenu("Toggle Wall Climb Ability")]
        public void ToggleWallClimbAbility()
        {
            if (playerController != null && playerController._stats != null)
            {
                playerController._stats.CanWallClimb = !playerController._stats.CanWallClimb;
                Log($"Wall climb ability: {(playerController._stats.CanWallClimb ? "ENABLED" : "DISABLED")}");
            }
        }

        [ContextMenu("Toggle Swimming Ability")]
        public void ToggleSwimmingAbility()
        {
            if (playerController != null && playerController._stats != null)
            {
                playerController._stats.CanSwim = !playerController._stats.CanSwim;
                Log($"Swimming ability: {(playerController._stats.CanSwim ? "ENABLED" : "DISABLED")}");
            }
        }

        [ContextMenu("Toggle Rope Climb Ability")]
        public void ToggleRopeClimbAbility()
        {
            if (playerController != null && playerController._stats != null)
            {
                playerController._stats.CanRopeClimb = !playerController._stats.CanRopeClimb;
                Log($"Rope climb ability: {(playerController._stats.CanRopeClimb ? "ENABLED" : "DISABLED")}");
            }
        }

        [ContextMenu("Toggle Glide Ability")]
        public void ToggleGlideAbility()
        {
            if (playerController != null && playerController._stats != null)
            {
                playerController._stats.CanGlide = !playerController._stats.CanGlide;
                Log($"Glide ability: {(playerController._stats.CanGlide ? "ENABLED" : "DISABLED")}");
            }
        }

        private void Update()
        {
            // Display current state in debug
            if (enableDebugLogging && playerController != null)
            {
                // This would require exposing the state machine's current state
                // For now, we can check the boolean flags
                string currentStates = "States: ";
                if (playerController._isSwimming) currentStates += "Swimming ";
                if (playerController._isDashing) currentStates += "Dashing ";
                if (playerController._isGliding) currentStates += "Gliding ";
                if (playerController._isWallClimbing) currentStates += "WallClimbing ";
                if (playerController._isClimbing) currentStates += "RopeClimbing ";
                if (playerController._grounded) currentStates += "Grounded ";
                
                // Only log when states change to avoid spam
                if (currentStates != _lastStateString)
                {
                    _lastStateString = currentStates;
                    Log(currentStates);
                }
            }
        }

        private string _lastStateString = "";
    }
}
